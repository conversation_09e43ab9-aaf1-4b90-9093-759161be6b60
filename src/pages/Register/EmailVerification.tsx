import React, { useMemo, useState, useRef } from 'react';
import { Card, Form, Input, Button, Typography, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useRegisterStore } from '@/store/registerStore';
import { api } from '@/services';
import FormButton from './coms/FormButton';
import { Link } from 'react-router-dom';
import type { Rule } from 'antd/es/form';
import logoIcon from '@/assets/logo.svg';

const COUNTDOWN_TIME = 60;
const EmailVerification: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { formData, updateFormData } = useRegisterStore();
  const [form] = Form.useForm();
  // 监听email字段变化,修改FormButton的disabled状态
  const email = Form.useWatch('email', form);
  const [isFirstSend, setIsFirstSend] = useState(true);
  const [countdown, setCountdown] = useState(0);
  const [isSending, setIsSending] = useState(false); // 是否正在发送验证码
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const handleSendVerificationCode = async () => {
    try {
      // 校验邮箱
      await form.validateFields(['email']);
      const email = form.getFieldValue('email');

      setIsSending(true);

      // 首先检查用户名（邮箱）是否可用
      const usernameCheckResponse = await api.auth.checkUsername(email);

      if (
        usernameCheckResponse.code === 200 &&
        usernameCheckResponse.body.trueOrFalse
      ) {
        // 用户名可用，发送验证码
        const response = await api.auth.sendSignupOtp({ recipient: email });

        if (response.code === 200 && response.body.trueOrFalse) {
          message.success(t('common.messages.verificationCodeSent'));
          setIsFirstSend(false);
          countDownStart();
        } else {
          message.error(t('common.messages.sendVerificationCodeFailed'));
        }
      } else {
        // 用户名不可用
        message.error(t('auth.register.step1.messages.emailAlreadyExists'));
      }
    } catch (error: any) {
      console.error('发送验证码失败:', error);
      message.error(
        error?.message || t('common.messages.sendVerificationCodeFailed')
      );
    } finally {
      setIsSending(false);
    }
  };
  const handleVerify = async () => {
    try {
      // 校验验证码
      await form.validateFields(['verificationCode']);
      const email = form.getFieldValue('email');
      const verificationCode = form.getFieldValue('verificationCode');

      // 调用验证OTP接口
      const response = await api.auth.verifyOtp({
        username: email,
        verificationCode,
      });

      if (response.code === 200 && response.body.trueOrFalse) {
        message.success(t('common.messages.verificationSuccess'));
        updateFormData({ email });
        navigate('/register/alias');
      } else {
        message.error(t('common.messages.verificationCodeInvalid'));
      }
    } catch (error: any) {
      console.error('验证码验证失败:', error);
      message.error(
        error?.message || t('common.messages.verificationCodeInvalid')
      );
    }
  };
  const countDownStart = () => {
    if (isSending) return;
    setCountdown(COUNTDOWN_TIME);
    timerRef.current = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(timerRef.current as NodeJS.Timeout);
          timerRef.current = null;
          setIsSending(false);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };
  const handleResendVerificationCode = async () => {
    try {
      // 校验邮箱
      await form.validateFields(['email']);
      const email = form.getFieldValue('email');

      setIsSending(true);

      // 调用发送验证码接口
      const response = await api.auth.sendSignupOtp({ recipient: email });

      if (response.code === 200 && response.body.trueOrFalse) {
        message.success(t('common.messages.verificationCodeSent'));
        countDownStart();
      } else {
        message.error(t('common.messages.sendVerificationCodeFailed'));
      }
    } catch (error: any) {
      console.error('重新发送验证码失败:', error);
      message.error(
        error?.message || t('common.messages.sendVerificationCodeFailed')
      );
    } finally {
      setIsSending(false);
    }
  };

  // 初始化表单值
  React.useEffect(() => {
    if (formData.email) {
      form.setFieldsValue({ email: formData.email });
    }
  }, [formData.email, form]);

  const rules: Rule[] = [
    {
      required: true,
    },
    {
      type: 'email',
      message: t('common.form.emailInvalid'),
    },
  ];
  return (
    <div className="h-520px">
      <div className="mb-8 text-center">
        <img src={logoIcon} alt="Yuequ Logo" className="h-[60px] w-[69px]" />
      </div>

      <h1 className="font-arial mb-16 text-center text-[32px] text-[#ff5e13] font-bold">
        {t('auth.register.title')}
      </h1>
      <Form form={form} layout="vertical" autoComplete="off">
        <Form.Item
          name="email"
          label={t('auth.register.step1.form.email')}
          rules={rules}
        >
          <Input
            placeholder={t('common.form.emailRequired')}
            className="s-form-input"
            size="large"
          />
        </Form.Item>

        {!isFirstSend && (
          <Form.Item
            name="verificationCode"
            label={t('common.verificationCode')}
            rules={[
              {
                required: true,
                message: t('common.form.enterVerificationCode'),
              },
            ]}
          >
            <Input
              placeholder={t('common.form.enterVerificationCode')}
              className="s-form-input"
              size="large"
            />
          </Form.Item>
        )}
        <Form.Item>
          <div className="mb-64px">
            <span className="font-inter text-[12px] text-[#656565] font-medium">
              {t('auth.register.step1.hasAccount')}
            </span>

            <Link
              to="/login"
              className="font-inter ml-1 text-[12px] font-medium !text-[#ff5e13] hover:underline"
            >
              {t('auth.register.step1.loginHere')}
            </Link>
          </div>
          {isFirstSend && (
            <FormButton
              htmlType="button"
              disabled={!email?.trim()}
              onClick={handleSendVerificationCode}
            >
              {t('common.buttons.sendVerificationCode')}
            </FormButton>
          )}
          {!isFirstSend && (
            <div className="flex justify-between gap-20px">
              <FormButton ghost htmlType="button" onClick={handleVerify}>
                {t('common.verify')}
              </FormButton>
              <FormButton
                htmlType="button"
                onClick={handleResendVerificationCode}
                disabled={isSending || countdown > 0}
              >
                {countdown > 0
                  ? `${countdown}s`
                  : isSending
                    ? t('common.sending')
                    : t('common.resendCode')}
              </FormButton>
            </div>
          )}
        </Form.Item>
      </Form>
      <div className="w-[496px]"></div>
    </div>
  );
};

export default EmailVerification;
