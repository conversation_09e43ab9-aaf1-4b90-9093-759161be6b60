import React from 'react';
import { Card, Form, Input, Button, Typography, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useRegisterStore } from '@/store/registerStore';
import { api } from '@/services';
import FormButton from './coms/FormButton';
import type { Rule, RuleObject } from 'antd/es/form';
import type { StoreValue } from 'antd/es/form/interface';

const AliasSetup: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { formData, updateFormData } = useRegisterStore();
  const [form] = Form.useForm();
  const alias = Form.useWatch('alias', form);

  // 初始化表单值
  React.useEffect(() => {
    if (formData.alias) {
      form.setFieldsValue({ alias: formData.alias });
    }
  }, [formData.alias, form]);

  // 检查是否有邮箱数据，如果没有则重定向到第一步
  React.useEffect(() => {
    if (!formData.email) {
      message.warning(
        t('auth.register.step4.messages.emailVerificationRequired')
      );
      navigate('/register/email');
    }
  }, [formData.email, navigate]);

  // 表单校验成功回调，更新表单数据并跳转到下一步
  const onFinish = async () => {
    try {
      const alias = form.getFieldValue('alias');

      // 检查别名可用性
      const response = await api.auth.checkAlias(alias);

      if (response.code === 200 && response.body.trueOrFalse) {
        // 别名可用
        updateFormData({ alias });
        navigate('/register/personal-info');
      } else {
        // 别名不可用，显示建议
        const suggestions = response.body.suggestions || [];
        const suggestionText =
          suggestions.length > 0
            ? t('auth.register.step2.messages.aliasSuggestions', {
                suggestions: suggestions.join(', '),
              })
            : '';
        message.error(
          t('auth.register.step2.messages.aliasUnavailable') + suggestionText
        );
      }
    } catch (error: any) {
      console.error('检查别名失败:', error);
      message.error(
        error?.message || t('auth.register.step2.messages.aliasCheckFailed')
      );
    }
  };

  // 自定义校验函数
  const aliasValidator = (
    _rule: RuleObject,
    value: StoreValue
  ): Promise<void> => {
    if (!value) {
      // required: true 会处理空值情况，这里可以省略
      return Promise.resolve();
    }
    // 检查1：首尾是否包含空格
    if (/^\s|\s$/.test(value)) {
      return Promise.reject(
        new Error('首尾不能包含空格')
        // new Error(t('auth.register.step2.form.aliasNoLeadingTrailingSpaces'))
      );
    }
    // 检查2：是否包含除字母、数字、空格外的非法字符
    if (/[^a-zA-Z0-9 ]/.test(value)) {
      return Promise.reject(
        new Error(t('auth.register.step2.form.aliasPattern'))
      );
    }

    return Promise.resolve();
  };

  const rules: Rule[] = [
    {
      required: true,
      message: t('auth.register.step2.form.aliasRequired'),
    },
    {
      min: 6,
      message: t('auth.register.step2.form.aliasMinLength', {
        min: 6,
      }),
    },
    {
      max: 12,
      message: t('auth.register.step2.form.aliasMaxLength', {
        max: 12,
      }),
    },
    {
      validator: aliasValidator,
    },
  ];
  return (
    <div className=" h-520px  ">
      <h1 className="font-arial mb-2 text-center text-[32px] text-[#ff5e13] font-bold">
        {t('auth.register.title')}
      </h1>

      <div className="mb-13 text-center text-12px text-label">
        {t('auth.register.step2.subtitle')}
      </div>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
      >
        <Form.Item
          label={t('auth.register.step2.form.alias')}
          name="alias"
          rules={rules}
          // 输入时自动处理多余的空格
          normalize={value => value.replace(/ {2,}/g, ' ')}
        >
          <Input
            placeholder={t('auth.register.step2.form.aliasPlaceholder')}
            className="s-form-input"
            size="large"
          />
        </Form.Item>
        <Form.Item>
          <div className="mb-64px">
            <span className="font-inter text-[12px] text-[#656565] font-medium">
              {t('auth.register.step2.form.aliasMinLength', {
                min: 6,
              })}
            </span>
          </div>

          <FormButton disabled={!alias?.trim()}>
            {t('auth.register.step2.buttons.next')}
          </FormButton>
        </Form.Item>
      </Form>
    </div>
  );
};

export default AliasSetup;
