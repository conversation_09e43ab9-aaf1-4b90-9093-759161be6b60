import React from 'react';
import PasswordForm from '@/components/PasswordForm';
import { Form, message } from 'antd';
import EditButton from './EditButton';

import { useLanguage } from '@/hooks/useLanguage';

interface PasswordProps {
  visible: boolean;
  onClose: () => void;
}

const EditProfilePassword: React.FC<PasswordProps> = ({ visible, onClose }) => {
  const { t } = useLanguage();
  const [password, setPassword] = React.useState('');
  const [loading, setLoading] = React.useState(false);
  const [passwordForm] = Form.useForm();

  if (!visible) return null;

  const onPasswordChange = (value: string) => {
    setPassword(value);
  };

  // 密码表单提交处理
  const onFinish = async (values: any) => {
    try {
      setLoading(true);
      // 注意：API文档中暂未提供修改密码的接口
      // TODO: 等待后端提供修改密码API后再实现此功能

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 暂时显示功能开发中的提示
      message.info('密码修改功能正在开发中，请稍后再试');
      onClose();
      passwordForm.resetFields();
    } catch (error) {
      console.error('密码修改失败:', error);
      message.error(t('common.saveFailed'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="my-20px -ml-148px">
      <Form
        form={passwordForm}
        requiredMark={false}
        onFinish={onFinish}
        autoComplete="off"
      >
        <PasswordForm
          password={password}
          onPasswordChange={onPasswordChange}
          inputClassName="s-profile-input"
          labelClassName="text-label  !text-12px !w-135px"
          passwordRequirementsClassName="pl-148px"
          showLabelColon={false}
          showTermsCheckbox={false}
          className="border"
        />
        <EditButton
          className="pl-148px"
          onCancel={() => {
            onClose();
            passwordForm.resetFields();
          }}
        />
      </Form>
    </div>
  );
};

export default EditProfilePassword;
