import React from 'react';
import { Layout as AntLayout, Button, Avatar, Dropdown } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '@/store/authStore';
import { useLanguage } from '@/hooks/useLanguage';
import SelectLanguage from '@/components/SelectLanguage';
import logoIcon from '@/assets/logo.svg';
import {
  SoundOutlined,
  UserOutlined,
  LogoutOutlined,
  SettingOutlined,
} from '@ant-design/icons';

const { Header: AntHeader } = AntLayout;
interface HeaderProps {
  fixed?: boolean;
  onClick?: () => void;
}

const Header: React.FC<HeaderProps> = ({ fixed = true, onClick }) => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { user, isAuthenticated } = useAuthStore();
  const handleLogin = () => {
    navigate('/login');
  };
  console.log('user', user);

  const handleRegister = () => {
    navigate('/register');
  };

  return (
    <header
      className={`flex items-center px-60px bg-page-bg justify-between shadow-sm h-120px  ${
        fixed ? 'fixed top-0 left-0 right-0 z-50' : ''
      } font-700 text-label text-16px `}
    >
      <div
        className="flex items-center cursor-pointer"
        onClick={() => navigate('/')}
      >
        <img src={logoIcon} alt="logo" className="w-54px h-54px" />
      </div>

      <div
        className="flex items-center space-x-4"
        style={{
          fontSize: '16px',
          fontWeight: '700',
          color: 'var(--color-label)',
        }}
      >
        <div className="cursor-pointer hover:text-primary w-120px text-center">
          {t('common.navigation.help')}
        </div>
        <div className="cursor-pointer hover:text-primary w-120px text-center">
          {t('common.navigation.aboutUs')}
        </div>
        {/* 语言切换下拉框 */}
        <SelectLanguage className="w-120px  text-center" />
        {isAuthenticated ? (
          // src={user?.avatarUrl}
          <Avatar
            size={44}
            onClick={onClick}
            className="cursor-pointer  select-none"
          >
            {user?.alias?.slice(0, 1)}
          </Avatar>
        ) : (
          <div className="space-x-2 flex items-center border-l-2 border-l-label border-l-solid">
            <div
              className="w-120px h-44px flex items-center justify-center cursor-pointer hover:text-primary"
              onClick={handleLogin}
            >
              {t('common.navigation.login')}
            </div>
            <Button
              type="primary"
              onClick={handleRegister}
              className="w-120px h-44px text-16px font-700"
            >
              {t('common.navigation.register')}
            </Button>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
