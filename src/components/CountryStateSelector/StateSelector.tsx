import React, { useEffect, useMemo, useRef, useState } from 'react';
import { type FormInstance, Select, Form, ConfigProvider, message } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';
import { api, metaUtils } from '@/services';
import type { Subdivision } from '@/types/api';
// import styles from './CountryStateSelector.module.css';

interface StateSelectorProps {
  placeholder?: string;
  size?: 'small' | 'middle' | 'large';
  form: FormInstance;
  countryFieldName?: string; // 允许自定义 country 字段名
  stateFieldName?: string; // 允许自定义 state 字段名
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
}

const StateSelector: React.FC<StateSelectorProps> = ({
  placeholder,
  size = 'large',
  form,
  countryFieldName = 'country',
  stateFieldName = 'state',
  value,
  onChange,
  className,
}) => {
  const { t } = useLanguage();
  const country = Form.useWatch(countryFieldName, form);
  const [subdivisions, setSubdivisions] = useState<Subdivision[]>([]);
  const [loading, setLoading] = useState(false);
  const [isTextInput, setIsTextInput] = useState(false);

  // 当国家改变时获取州/省列表
  useEffect(() => {
    const fetchSubdivisions = async () => {
      if (!country) {
        setSubdivisions([]);
        setIsTextInput(false);
        return;
      }

      // 检查是否支持州/省列表
      if (!metaUtils.isSupportedCountryForSubdivisions(country)) {
        setSubdivisions([]);
        setIsTextInput(true); // 使用文本输入
        return;
      }

      try {
        setLoading(true);
        setIsTextInput(false);
        const response = await api.meta.getSubdivisions(country);

        if (response.code === 200) {
          setSubdivisions(response.body || []);
        } else {
          message.error(t('common.messages.loadSubdivisionsFailed'));
          setSubdivisions([]);
        }
      } catch (error: any) {
        console.error('获取州/省列表失败:', error);
        message.error(
          error?.message || t('common.messages.loadSubdivisionsFailed')
        );
        setSubdivisions([]);
      } finally {
        setLoading(false);
      }
    };

    fetchSubdivisions();
  }, [country, t]);

  const options = useMemo(() => {
    return subdivisions.map(subdivision => ({
      label: subdivision.name,
      value: subdivision.code,
    }));
  }, [subdivisions]);

  // 当国家不支持州/省列表时，显示文本输入框
  if (isTextInput) {
    return (
      <input
        type="text"
        placeholder={placeholder || t('common.form.selectState')}
        className={
          className ||
          `s-form-selector-hover rounded-6px h-54px text-14px flex-shrink-0 flex-basis-88px border border-gray-300 px-3`
        }
        value={value}
        onChange={e => onChange?.(e.target.value)}
        disabled={!country}
      />
    );
  }

  return (
    <Select
      placeholder={placeholder || t('common.form.selectState')}
      size={size}
      className={
        className ||
        `s-form-selector-hover rounded-6px !h-54px text-14px flex-shrink-0 flex-basis-88px `
      }
      options={options}
      value={value}
      onChange={onChange}
      loading={loading}
      disabled={!country} // 当没有选择国家时禁用
      showSearch
      filterOption={(input, option) =>
        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
      }
    />
  );
};

export default StateSelector;
