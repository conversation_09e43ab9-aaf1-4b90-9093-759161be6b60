import React, { Suspense, useEffect } from 'react';
import { ConfigProvider, Spin, Button } from 'antd';
// import '@ant-design/v5-patch-for-react-19';
import 'antd/dist/reset.css';
import zhCN from 'antd/locale/zh_CN';
import enUS from 'antd/locale/en_US';
import AppRouter from './router';
import { useAppStore } from './store';
import './locales'; // 初始化 i18n
import './App.css';
import { useLanguage } from '@/hooks/useLanguage';
import GlobalLoading from '@/components/GlobalLoading';

/**
 * 主应用组件
 * - 配置 Ant Design 中文语言包
 * - 集成路由系统和国际化
 * - 全局状态管理已在各组件中使用
 */
const App: React.FC = () => {
  const { language } = useAppStore();
  const { t } = useLanguage();

  // 根据当前语言选择 Ant Design 语言包
  const antdLocale = language === 'zh' ? zhCN : enUS;
  // 配置 Ant Design form 的验证消息
  const validateMessages = {
    required: t('common.form.requiredMsg', { label: '${label}' }),
  };
  return (
    <ConfigProvider
      locale={antdLocale}
      form={{ validateMessages }}
      theme={{
        cssVar: true,
        token: {
          colorPrimary: '#ff5e13',
          colorTextBase: '#656565',
          // 统一字体设置
          fontFamily: 'var(--font-family)',
          fontFamilyCode: 'var(--font-family)',
        },
        components: {
          Modal: {
            // colorBgElevated: 'var(--color-page-bg)',
            contentBg: 'var(--color-page-bg)',
            headerBg: 'var(--color-page-bg)',
            boxShadow:
              '0 1px 0 0 rgba(255, 255, 255, 0.25) inset, 0 13px 9px -3px rgba(0, 0, 0, 0.25)',
          },
          Layout: {
            headerHeight: 120,
            headerBg: 'var(--color-page-bg)',
            headerColor: 'var(--color-label)',
            headerPadding: 0,
            bodyBg: 'var(--color-page-bg)',
            siderBg: 'var(--color-page-bg)',
          },
          Button: {
            // colorPrimary: 'var(--color-primary)', // 主按钮颜色
            colorPrimaryHover: 'var(--color-primary-active)', // 主按钮悬停颜色
            colorPrimaryActive: 'var(--color-primary-active)', // 主按钮激活颜色
            primaryColor: 'var(--color-primary-text)', // 主按钮文字颜色
            colorBgContainerDisabled: 'var(--color-primary)', // 主按钮禁用颜色使用完整颜色
            borderColorDisabled: 'transparent', // 主按钮禁用边框颜色设为透明
            colorTextDisabled: 'var(--color-primary-text)', // 主按钮禁用文字颜色
            contentFontSizeLG: 18, // 主按钮大号文字大小
            controlHeightLG: 63,
          },
          Form: {
            labelColor: 'var(--color-label)',
          },
          Select: {
            // colorBgContainerDisabled: '#888888',
          },
          Dropdown: {
            colorPrimary: 'var(--color-primary)',
            controlItemBgHover: 'var(--color-primary)',
            controlItemBgActiveHover: 'var(--color-primary)',
            colorText: 'var(--color-label)',
            colorBgElevated: 'rgba(0,0,0,0.5)',
            fontSize: 16,
          },
          Space: {
            colorText: 'var(--color-label)',
          },
        },
      }}
    >
      <div className="App">
        <Suspense
          fallback={
            <div className="min-h-screen flex items-center justify-center">
              <Spin size="large" />
            </div>
          }
        >
          <AppRouter />
        </Suspense>
        <GlobalLoading />
      </div>
    </ConfigProvider>
  );
};

export default App;
