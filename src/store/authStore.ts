import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';
import type {} from '@redux-devtools/extension'; // devtools 类型支持
import type { User, ExtendedLoginRequest } from '@/types';
import type { Permission } from '@/router/routeMap';
import { api } from '@/services';
import { isArtist, isInvestor } from '@/utils/roleUtils';

export type { User };

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean; // 登录状态
  isArtist: boolean;
  isInvestor: boolean;
  login: (loginData: ExtendedLoginRequest) => Promise<void>;
  setAuthData: (token: string, user: User) => void; // 直接设置认证数据
  fetchUserInfo: () => void;
  initializeAuth: () => void;
  logout: () => void;
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set, get) => ({
        user: null,
        token: null,
        isAuthenticated: false,

        // 计算属性
        get isArtist() {
          // console.log('isAuthenticated---', get()?.isAuthenticated);

          // console.log('get().user----', get()?.user);

          return isArtist(get()?.user);
        },
        get isInvestor() {
          return isInvestor(get()?.user);
        },
        login: async loginData => {
          try {
            // 1. 判断是密码登录还是验证码登录
            let result;
            if (loginData.verificationCode) {
              // 验证码登录
              result = await api.auth.loginWithOtp({
                username: loginData.username,
                verificationCode: loginData.verificationCode,
              });
            } else {
              // 密码登录
              result = await api.auth.login({
                username: loginData.username,
                password: loginData.password,
              });
            }

            if (result.code === 200) {
              const authData = result.body;

              // 构建用户对象
              const user: User = {
                accountId: authData.accountId,
                email: '', // 登录接口不返回email，需要从profile接口获取
                alias: authData.alias,
                firstName: '',
                lastName: '',
                displayName: authData.displayName,
                avatarUrl: authData.avatarUrl || null,
                stageName: authData.stageName,
                roles: authData.roles,
              };

              set({
                token: authData.token,
                isAuthenticated: true,
                user: user,
              });
              localStorage.setItem('token', authData.token);

              // 2. 尝试获取完整用户信息（可选）
              try {
                get().fetchUserInfo();
                console.log('获取完整用户数据成功');
              } catch (userInfoError) {
                console.error(
                  '获取完整用户信息失败，但登录成功:',
                  userInfoError
                );
                // 不抛出错误，允许登录继续
              }
            } else {
              // 根据错误代码提供更具体的错误信息
              let errorMessage = '登录失败';
              switch (result.code) {
                case 400:
                  errorMessage = '请求参数错误，请检查用户名和密码格式';
                  break;
                case 401:
                  errorMessage = '用户名或密码错误';
                  break;
                case 403:
                  errorMessage = '账户被禁用，请联系管理员';
                  break;
                case 404:
                  errorMessage = '用户不存在';
                  break;
                case 500:
                  errorMessage = '服务器错误，请稍后重试';
                  break;
                default:
                  errorMessage = result.message || '登录失败，请重试';
              }
              throw new Error(errorMessage);
            }
          } catch (error: any) {
            console.error('登录失败----', error);

            // 如果是网络错误或其他非API错误
            if (!error.message || error.message.includes('fetch')) {
              throw new Error('网络连接失败，请检查网络设置');
            }

            throw error;
          }
        },
        // 直接设置认证数据（用于注册成功后）
        setAuthData: (token: string, user: User) => {
          set({
            token,
            user,
            isAuthenticated: true,
          });
          localStorage.setItem('token', token);
        },
        // 获取用户信息
        fetchUserInfo: () => {
          try {
            // 如果需要获取更多用户信息，可以在这里实现
            console.log('fetchUserInfo: 不再需要获取权限信息');
          } catch (error) {
            console.error('获取用户信息失败:', error);
            throw error;
          }
        },
        initializeAuth: () => {
          const token = localStorage.getItem('token');
          if (token) {
            set({ token, isAuthenticated: true });
            get().fetchUserInfo();
          } else {
            // Token 无效，清除登录
            get().logout();
          }
        },
        logout: () => {
          set({
            user: null,
            token: null,
            isAuthenticated: false,
          });
          localStorage.removeItem('token');
        },
        // isArtist: () => {
        //   // 判断用户是否为艺人
        //   return isArtist(get().user);
        // },
        // isInvestor: () => {
        //   // 判断用户是否为投资人
        //   return isInvestor(get().user);
        // },
      }),
      {
        name: 'auth-store', // localStorage持久化的名称
      }
    ),
    {
      name: 'AuthStore', // DevTools 中显示的名称
    }
  )
);
