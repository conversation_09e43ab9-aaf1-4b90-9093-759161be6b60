import type { UserProfileResponse } from '@/types/api';
import i18n from '@/locales';
// 检查是否为开发环境
export const isDevelopment = import.meta.env.DEV;

// 获取时区
export const getSystemTimeZone = () => {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
};

// 解析包含国家代码的手机号
export const parsePhoneNumber = (phone: string) => {
  const phoneArr = `${phone}`.split('~');
  return { phone: +phoneArr[0], phoneCountry: phoneArr[1] || 'CN' };
};

// 如果没输入手机号,返回'',有手机号返回`手机号~国家代码`
export const getPhoneNumber = (phone: string, phoneCountry: string) => {
  return phone ? `${phone}~${phoneCountry}` : '';
};

// 获取显示地址
export const getDisplayAddress = (userInfo: UserProfileResponse) => {
  const MAX_LENGTH = 60; // 定义最大显示长度

  if (!userInfo) {
    return '';
  }

  let fullAddress = '';

  switch (userInfo.countryCode) {
    case 'CN': {
      // 中国地址格式 最终格式 ： 中国省份城市街道 邮政编码
      const mainAddressComponents = [
        userInfo.stateProvince,
        userInfo.addressLine2,
        userInfo.addressLine1,
      ];
      // 去重 防止addressLine2  和  stateProvince 重复
      const uniqueMainParts = [
        ...new Set(mainAddressComponents.filter(Boolean)),
      ];
      const mainAddress = [i18n.t('common.china'), ...uniqueMainParts].join('');
      fullAddress = [mainAddress, userInfo.postalZipCode]
        .filter(Boolean)
        .join(' ');
      break;
    }

    default:
      // 为其他国家提供一个通用的后备格式
      // 最终格式: street, city, state, country and postal code
      // 最终格式 ：街道, 城市, 州, 国家 邮政编码
      fullAddress = [
        userInfo.addressLine1,
        userInfo.addressLine2,
        userInfo.stateProvince,
        userInfo.countryCode,
      ]
        .filter(Boolean)
        .join(', ');
      fullAddress =
        fullAddress +
        (userInfo.postalZipCode ? ` ${userInfo.postalZipCode}` : '');
      break;
  }

  // 检查总长度并进行截断
  if (fullAddress.length > MAX_LENGTH) {
    return `${fullAddress.slice(0, MAX_LENGTH)}...`;
  }

  return fullAddress;
};
