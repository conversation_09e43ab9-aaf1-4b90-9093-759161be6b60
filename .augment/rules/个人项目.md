---
type: 'always_apply'
priority: 'high'
description: '个人项目开发原则和代码风格指导'
---

# 个人项目开发原则

## 项目性质
个人项目，重点关注功能实现和学习成长，避免企业级复杂架构。

## 核心原则

### 1. 简洁优先
- 能用简单方案解决的问题，不要复杂化
- 避免不必要的抽象层和设计模式
- 直接实现功能，减少中间环节

### 2. 实用主义
- 功能正确比代码"优雅"更重要
- 先让功能跑起来，再考虑优化
- 复制粘贴代码而非过度抽象

### 3. 技术约束
- 使用现有技术栈：React + Ant Design + UnoCSS
- 优先使用 Ant Design 默认组件和配置
- 避免引入新的复杂依赖

### 4. 代码组织
- 组件文件保持简短（200行以内）
- 相关逻辑放在同一文件中
- 避免过度拆分和复杂目录结构

## 具体要求

**✅ 推荐：**
- 内联样式和简单组件
- 直接在组件中处理状态
- 使用库的默认配置

**❌ 避免：**
- 复杂的 HOC 和 render props
- 过度使用设计模式
- 为了"复用"而强行抽象
- 过早性能优化

## 实施
所有代码生成和建议都应遵循以上原则，优先选择简单直接的方案。
